# Generated by Django 4.2.10 on 2025-05-18 09:05

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Chain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('chain', models.CharField(max_length=50, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_testnet', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': '区块链',
                'verbose_name_plural': '区块链',
            },
        ),
        migrations.CreateModel(
            name='Token',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('symbol', models.CharField(max_length=20, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('address', models.CharField(blank=True, max_length=100)),
                ('decimals', models.IntegerField(default=18)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('chain', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tokens', to='CryptoAnalyst.chain')),
            ],
        ),
        migrations.CreateModel(
            name='TechnicalAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('rsi', models.FloatField(null=True)),
                ('macd_line', models.FloatField(null=True)),
                ('macd_signal', models.FloatField(null=True)),
                ('macd_histogram', models.FloatField(null=True)),
                ('bollinger_upper', models.FloatField(null=True)),
                ('bollinger_middle', models.FloatField(null=True)),
                ('bollinger_lower', models.FloatField(null=True)),
                ('bias', models.FloatField(null=True)),
                ('psy', models.FloatField(null=True)),
                ('dmi_plus', models.FloatField(null=True)),
                ('dmi_minus', models.FloatField(null=True)),
                ('dmi_adx', models.FloatField(null=True)),
                ('vwap', models.FloatField(null=True)),
                ('funding_rate', models.FloatField(null=True)),
                ('exchange_netflow', models.FloatField(null=True)),
                ('nupl', models.FloatField(null=True)),
                ('mayer_multiple', models.FloatField(null=True)),
                ('period_start', models.DateTimeField(default=django.utils.timezone.now, null=True)),
                ('token', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='technical_analysis', to='CryptoAnalyst.token')),
            ],
            options={
                'ordering': ['-timestamp'],
                'get_latest_by': 'timestamp',
                'unique_together': {('token', 'period_start')},
            },
        ),
        migrations.CreateModel(
            name='AnalysisReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('snapshot_price', models.FloatField(default=0)),
                ('language', models.CharField(choices=[('zh-CN', '简体中文'), ('en-US', 'English'), ('ja-JP', '日本語'), ('ko-KR', '한국어')], default='en-US', max_length=10, verbose_name='报告语言')),
                ('trend_up_probability', models.IntegerField(default=0)),
                ('trend_sideways_probability', models.IntegerField(default=0)),
                ('trend_down_probability', models.IntegerField(default=0)),
                ('trend_summary', models.TextField(blank=True)),
                ('rsi_analysis', models.TextField(blank=True)),
                ('rsi_support_trend', models.CharField(blank=True, max_length=20)),
                ('macd_analysis', models.TextField(blank=True)),
                ('macd_support_trend', models.CharField(blank=True, max_length=20)),
                ('bollinger_analysis', models.TextField(blank=True)),
                ('bollinger_support_trend', models.CharField(blank=True, max_length=20)),
                ('bias_analysis', models.TextField(blank=True)),
                ('bias_support_trend', models.CharField(blank=True, max_length=20)),
                ('psy_analysis', models.TextField(blank=True)),
                ('psy_support_trend', models.CharField(blank=True, max_length=20)),
                ('dmi_analysis', models.TextField(blank=True)),
                ('dmi_support_trend', models.CharField(blank=True, max_length=20)),
                ('vwap_analysis', models.TextField(blank=True)),
                ('vwap_support_trend', models.CharField(blank=True, max_length=20)),
                ('funding_rate_analysis', models.TextField(blank=True)),
                ('funding_rate_support_trend', models.CharField(blank=True, max_length=20)),
                ('exchange_netflow_analysis', models.TextField(blank=True)),
                ('exchange_netflow_support_trend', models.CharField(blank=True, max_length=20)),
                ('nupl_analysis', models.TextField(blank=True)),
                ('nupl_support_trend', models.CharField(blank=True, max_length=20)),
                ('mayer_multiple_analysis', models.TextField(blank=True)),
                ('mayer_multiple_support_trend', models.CharField(blank=True, max_length=20)),
                ('trading_action', models.CharField(default='等待', max_length=20)),
                ('trading_reason', models.TextField(blank=True)),
                ('entry_price', models.FloatField(default=0)),
                ('stop_loss', models.FloatField(default=0)),
                ('take_profit', models.FloatField(default=0)),
                ('risk_level', models.CharField(default='中', max_length=10)),
                ('risk_score', models.IntegerField(default=50)),
                ('risk_details', models.JSONField(default=list)),
                ('technical_analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_reports', to='CryptoAnalyst.technicalanalysis')),
                ('token', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_reports', to='CryptoAnalyst.token')),
            ],
            options={
                'ordering': ['-timestamp'],
                'get_latest_by': 'timestamp',
            },
        ),
    ]
