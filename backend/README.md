# 加密货币分析系统

## 维度数据获取方式

### 1. 市场数据维度 (MarketDataService)
- **数据来源**：Binance API
- **获取方式**：
  - 实时价格：`get_realtime_price()`
  - 历史K线：`get_historical_klines()`
  - 24小时交易数据：`get_ticker()`
- **主要指标**：
  - 未实现盈亏(NUPL)：基于200天历史数据计算
  - 交易所净流入：基于24小时买卖量计算
  - 梅耶倍数：基于200日移动平均线计算
  - 恐慌贪婪指数：通过alternative.me API获取

### 2. 代币数据维度 (TokenDataService)
- **数据来源**：CoinGecko API
- **获取方式**：
  - 代币详细信息：`_get_token_info()`
  - 市场数据：`_get_market_data()`
  - 价格历史：`_get_price_history()`
  - 社交媒体数据：`_get_social_data()`
- **主要指标**：
  - 市场数据：价格、市值、交易量、供应量等
  - 社交媒体数据：Twitter、Reddit、Telegram等平台数据

### 3. 技术分析维度 (TechnicalAnalysisService)
- **数据来源**：Binance API
- **获取方式**：
  - K线数据：`get_klines()`
- **主要指标**：
  - RSI：相对强弱指标
  - MACD：移动平均收敛散度
  - 布林带：波动性指标
  - 移动平均线：SMA20、SMA50、SMA200

### 数据缓存策略
- 所有服务都实现了缓存机制
- 缓存时间：
  - 市场数据：5分钟
  - 技术指标：15分钟
  - 代币数据：根据API限制动态调整

### 错误处理机制
- 所有服务都实现了多级降级策略：
  1. 优先从缓存获取数据
  2. 缓存未命中时调用API
  3. API失败时尝试从数据库获取
  4. 数据库失败时返回默认值
- 详细的错误日志记录
- 优雅的降级处理确保服务可用性

### 数据更新频率
- 实时数据：每5分钟更新一次
- 技术指标：每15分钟更新一次
- 历史数据：每日更新
- 社交媒体数据：每小时更新
