[2025-05-20 10:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 10:10:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 10:15:00,076: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 10:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 10:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 10:30:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 10:45:38,784: INFO/MainProcess] beat: Starting...
[2025-05-20 10:46:00,035: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 10:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 10:53:00,902: INFO/MainProcess] beat: Starting...
[2025-05-20 10:55:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 10:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:01:00,015: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:06:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:15:00,060: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:45:00,068: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 11:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 11:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-12 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:00:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:05:00,000: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-12 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:05:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:16:00,074: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:20:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:46:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 12:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 12:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:31:00,121: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:36:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 13:55:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 13:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:10:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:15:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:20:00,009: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:50:00,091: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:51:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 14:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 14:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:01:00,007: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:06:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:10:00,008: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:11:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:16:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:20:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:31:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:50:00,056: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:51:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 15:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 15:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:05:00,075: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:20:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:45:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 16:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 16:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:00:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:06:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:21:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:40:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 17:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 17:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:06:00,039: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:10:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:30:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:36:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:51:00,025: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 18:55:00,013: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 18:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:21:00,032: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 19:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 19:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:06:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:10:00,068: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:25:00,056: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:26:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:30:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:31:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:35:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:40:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:50:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 20:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 20:56:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:00:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:05:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:06:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:10:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:15:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:26:00,055: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:36:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:40:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:46:00,007: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:51:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 21:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 21:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:10:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:35:00,020: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:51:00,008: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 22:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 22:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:15:00,139: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:50:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-20 23:55:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-20 23:56:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:00:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-0 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:00:00,025: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:05:00,000: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-0 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:05:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:06:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:30:00,089: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:35:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:40:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:45:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:50:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 00:55:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 00:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:00:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:05:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:21:00,005: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 01:55:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 01:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:00:00,055: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:35:00,007: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 02:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 02:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:00:00,020: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:06:00,014: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 03:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 03:56:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 04:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 04:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:00:00,068: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:11:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:21:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:46:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 05:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 05:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:00:00,219: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:05:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:15:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:16:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:35:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:46:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:50:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 06:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 06:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:00:00,008: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:05:00,013: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:11:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:15:00,078: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:20:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:25:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:30:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:31:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:35:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:36:00,006: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:40:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 07:55:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 07:56:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:00:00,078: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:05:00,008: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:10:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:20:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:30:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:45:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:46:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 08:55:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 08:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:35:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:40:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:45:00,089: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 09:55:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 09:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:00:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:05:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:10:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:15:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:20:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:25:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:30:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:35:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:45:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:50:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 10:55:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 10:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:00:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:05:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:15:00,046: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:16:00,006: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:20:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:25:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:26:00,017: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:30:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:35:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:40:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:45:00,055: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:50:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 11:55:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 11:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:00:00,036: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-12 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:00:00,046: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:01:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:05:00,003: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-12 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:10:00,008: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:20:00,009: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:21:00,009: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:25:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:30:00,008: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:35:00,032: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:40:00,019: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:45:00,030: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:46:00,006: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:50:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 12:55:00,025: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 12:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:00:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:05:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:10:00,033: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:11:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:15:00,007: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:20:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:21:00,007: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:25:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:30:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:40:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:45:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:46:00,023: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 13:55:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 13:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:00:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:05:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:10:00,026: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:11:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:15:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:20:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:25:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:26:00,007: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:30:00,016: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:31:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:35:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:40:00,027: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:45:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:46:00,024: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:50:00,133: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 14:55:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 14:56:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:00:00,025: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:01:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:05:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:06:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:10:00,032: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:15:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:20:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:21:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:25:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:30:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:31:00,005: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:36:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:40:00,009: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:45:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:46:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:50:00,067: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:51:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 15:55:00,019: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 15:56:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:00:00,044: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:05:00,040: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:06:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:10:00,007: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:11:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:15:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:20:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:21:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:25:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:30:00,024: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:35:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:36:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:40:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:45:00,013: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:46:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:50:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:51:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 16:55:00,018: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 16:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:05:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:06:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:10:00,013: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:16:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:21:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:25:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:26:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:30:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:31:00,004: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:36:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:51:00,004: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 17:55:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 17:56:00,011: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:00:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:05:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:06:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:11:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:15:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:20:00,018: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:21:00,009: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:31:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:35:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:36:00,011: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:40:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:45:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:50:00,009: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:51:00,015: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 18:55:00,013: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 18:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:00:00,019: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:06:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:10:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:16:00,011: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:26:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:36:00,022: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 19:55:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 19:56:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:00:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:01:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:06:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:10:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:11:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:15:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:20:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:21:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:31:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:35:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:36:00,157: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:40:00,021: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:46:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 20:55:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 20:56:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:00:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:10:00,088: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:11:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:15:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:20:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:25:00,020: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:26:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:30:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:31:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:35:00,018: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:40:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 21:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 21:56:00,018: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:05:00,013: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:06:00,011: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:16:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:25:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:35:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:36:00,011: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:45:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:46:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 22:55:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 22:56:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:00:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:01:00,007: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:10:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:11:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:15:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:16:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:20:00,013: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:21:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:25:00,127: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:31:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:36:00,014: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:40:00,080: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:41:00,015: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:45:00,021: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:50:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-21 23:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-21 23:56:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:00:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-0 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:00:00,041: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:05:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:05:00,051: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-0 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:10:00,018: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:15:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:16:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:25:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:26:00,004: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:35:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:36:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:40:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:45:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:50:00,019: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 00:55:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 00:56:00,052: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:00:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:05:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:06:00,011: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:10:00,033: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:11:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:15:00,007: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:20:00,013: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:25:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:26:00,011: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:30:00,016: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:35:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:40:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 01:55:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 01:56:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:00:00,082: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:01:00,004: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:05:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:06:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:10:00,020: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:11:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:15:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:16:00,007: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:21:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:25:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:26:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:30:00,050: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:31:00,071: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:35:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:40:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:45:00,060: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:46:00,008: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:50:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 02:55:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 02:56:00,014: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:05:00,019: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:11:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:15:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:16:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:20:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:25:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:26:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:30:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:31:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:35:00,016: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:40:00,029: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:46:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 03:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 03:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:00:00,054: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:01:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:05:00,018: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:10:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:15:00,065: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:20:00,021: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:25:00,026: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:30:00,019: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:35:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:36:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:40:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:45:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:50:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 04:55:00,020: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 04:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:00:00,039: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:05:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:10:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:11:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:15:00,046: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:16:00,005: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:20:00,021: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:25:00,018: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:30:00,020: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:31:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:35:00,021: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:36:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:40:00,024: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:45:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:46:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:50:00,020: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 05:55:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 05:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:00:00,018: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:01:00,011: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:10:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:11:00,023: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:15:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:16:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:20:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:25:00,025: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:26:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:30:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:31:00,014: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:35:00,020: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:40:00,026: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:41:00,005: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:45:00,021: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:50:00,016: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 06:55:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 06:56:00,015: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:00:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:10:00,007: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:11:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:15:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:16:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:20:00,040: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:21:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:25:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:26:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:30:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:31:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:35:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:40:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:45:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:46:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:50:00,016: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 07:55:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 07:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:00:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:05:00,051: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:10:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:15:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:21:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:26:00,014: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:30:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:40:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:45:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:50:00,047: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:51:00,009: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 08:55:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 08:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:01:00,017: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:06:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:10:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:15:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:20:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:35:00,071: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:40:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:45:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:50:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:51:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 09:55:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 09:56:00,016: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:00:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:05:00,016: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:06:00,009: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:15:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:20:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:30:00,030: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:35:00,028: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:36:00,024: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:40:00,027: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:50:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:51:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 10:55:00,009: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 10:56:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:00:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:05:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:06:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:10:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:15:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:20:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:21:00,202: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:25:00,055: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:26:00,020: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:31:00,008: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:35:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:36:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:40:00,009: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:45:00,009: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:51:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 11:55:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 11:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:00:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-12 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:00:00,050: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:05:00,016: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:05:00,039: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-12 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:06:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:10:00,047: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:15:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:20:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:21:00,008: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:26:00,016: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:30:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:31:00,024: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:35:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:40:00,086: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:45:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:46:00,012: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:50:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:51:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 12:55:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 12:56:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:00:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:01:00,019: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:05:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:06:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:10:00,048: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:15:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:16:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:20:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:21:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:25:00,044: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:30:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:35:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:36:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:40:00,008: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:45:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:50:00,025: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 13:55:00,018: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 13:56:00,171: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:00:00,058: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:01:00,018: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:05:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:06:00,017: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:10:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:11:00,083: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:15:00,082: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:16:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:20:00,025: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:21:00,034: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:25:00,025: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:26:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:30:00,024: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:31:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:35:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:36:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:40:00,042: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:45:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:46:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:50:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:51:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 14:55:00,023: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 14:56:00,004: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:00:00,088: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:05:00,031: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:06:00,010: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:10:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:11:00,005: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:15:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:16:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:20:00,017: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:21:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:25:00,013: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:30:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:31:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:35:00,032: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:36:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:40:00,016: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:45:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:46:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:50:00,025: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:51:00,020: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 15:55:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 15:56:00,011: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:05:00,018: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:10:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:11:00,034: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:15:00,125: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:16:00,015: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:20:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:21:00,033: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:25:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:30:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:35:00,100: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:36:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:40:00,028: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:41:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:46:00,009: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:50:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 16:55:00,016: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 16:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:00:00,076: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:05:00,022: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:10:00,019: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:20:00,026: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:25:00,011: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:30:00,048: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:31:00,009: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:35:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:40:00,021: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:50:00,015: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:51:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 17:55:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 17:56:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:00:00,012: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:05:00,014: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:10:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:31:00,043: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:36:00,024: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:40:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:45:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:50:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 18:55:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 18:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 19:00:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 19:01:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 19:20:30,925: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 19:20:30,929: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 19:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 19:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 19:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 19:30:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 19:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 19:35:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 19:53:13,424: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 19:53:13,428: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 20:10:48,059: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 20:10:48,064: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 20:26:42,699: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 20:26:42,704: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 20:46:39,564: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 20:46:39,568: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 20:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 20:51:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 20:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 20:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 21:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 21:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 21:05:00,005: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 21:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 21:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 21:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 21:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 21:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 21:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 21:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 21:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 21:26:00,004: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 21:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 21:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 21:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 21:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 21:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 21:41:00,004: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 22:17:38,727: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 22:17:38,732: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 22:50:52,435: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 22:50:52,440: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 23:06:37,435: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 23:06:37,440: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 23:25:28,772: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 23:25:28,779: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 23:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 23:30:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 23:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 23:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 23:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 23:40:00,046: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 23:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 23:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 23:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 23:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 23:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-22 23:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-22 23:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:00:00,010: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-0 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:05:00,000: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-0 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:05:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:26:00,028: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:41:00,013: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 00:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 00:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:00:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:06:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:40:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:46:00,005: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 01:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 01:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:00:00,070: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:21:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:26:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:30:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:31:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:40:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:41:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:45:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:46:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:50:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:51:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 02:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 02:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:00:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:01:00,001: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:05:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:06:00,004: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:10:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:11:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:15:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:16:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:20:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:21:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:25:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:26:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:30:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:31:00,005: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:35:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:36:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:40:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:41:00,002: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:45:00,003: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:46:00,004: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:50:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:51:00,003: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 03:55:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 03:56:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 04:00:00,132: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 04:01:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 04:05:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 04:06:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 04:10:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 04:11:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 04:15:00,006: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 04:16:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 04:20:00,000: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-every-5-min (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-23 04:21:00,000: INFO/MainProcess] Scheduler: Sending due task update-coze-analysis-every-5-min (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-23 13:15:45,615: INFO/MainProcess] beat: Starting...
[2025-05-23 13:37:31,002: INFO/MainProcess] beat: Starting...
[2025-05-24 00:00:00,087: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-0 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-24 00:05:00,001: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-0 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-24 12:00:00,021: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-12 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-24 12:05:00,000: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-12 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-25 00:00:00,002: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-0 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-25 00:05:00,033: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-0 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-25 12:00:00,001: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-12 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-25 12:05:00,003: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-12 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-26 00:00:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-0 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-26 00:05:00,000: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-0 (CryptoAnalyst.tasks.generate_analysis_reports)
[2025-05-26 12:00:00,004: INFO/MainProcess] Scheduler: Sending due task update-technical-analysis-at-12 (CryptoAnalyst.tasks.update_technical_analysis)
[2025-05-26 12:05:00,000: INFO/MainProcess] Scheduler: Sending due task generate-analysis-reports-at-12 (CryptoAnalyst.tasks.generate_analysis_reports)
