{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}
{% load user_tags %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .leaderboard-container {
            margin: 20px 0;
        }
        .leaderboard-table {
            width: 100%;
            border-collapse: collapse;
        }
        .leaderboard-table th, .leaderboard-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .leaderboard-table th {
            background-color: #f2f2f2;
            cursor: pointer;
        }
        .leaderboard-table tr:hover {
            background-color: #f5f5f5;
        }
        .filter-form {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .filter-form .form-row {
            display: inline-block;
            margin-right: 15px;
        }
        .filter-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .filter-form input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .filter-form button {
            padding: 5px 10px;
            background-color: #417690;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .filter-form button:hover {
            background-color: #2b5070;
        }
        .export-btn {
            margin-top: 10px;
            padding: 5px 10px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 3px;
            text-decoration: none;
            display: inline-block;
        }
        .export-btn:hover {
            background-color: #218838;
            color: white;
        }
        .rank-column {
            text-align: center;
            font-weight: bold;
        }
        .points-column {
            text-align: center;
            font-weight: bold;
            color: #d9534f;
        }
        .top-3 {
            background-color: #fff3cd;
        }
        .pagination {
            margin-top: 20px;
            text-align: center;
        }
        .pagination a {
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #ddd;
            text-decoration: none;
        }
        .pagination a.active {
            background-color: #417690;
            color: white;
        }
    </style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label='user' %}">用户</a>
    &rsaquo; 用户积分排行榜
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <h1>用户积分排行榜</h1>

    <div class="filter-form">
        <form method="get">
            <div class="form-row">
                <label for="min_points">最小积分:</label>
                <input type="number" name="min_points" id="min_points" value="{{ min_points }}" min="0">
            </div>
            <div class="form-row">
                <label for="max_points">最大积分:</label>
                <input type="number" name="max_points" id="max_points" value="{{ max_points }}" min="0">
            </div>
            <div class="form-row">
                <label for="order_by">排序方式:</label>
                <select name="order_by" id="order_by">
                    <option value="-points" {% if order_by == '-points' %}selected{% endif %}>积分降序</option>
                    <option value="points" {% if order_by == 'points' %}selected{% endif %}>积分升序</option>
                </select>
            </div>
            <button type="submit">筛选</button>
            <a href="?export=csv" class="export-btn">导出CSV</a>
        </form>
    </div>

    <div class="leaderboard-container">
        <table class="leaderboard-table">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>用户名</th>
                    <th>邮箱</th>
                    <th>积分</th>
                    <th>邀请人数</th>
                    <th>注册时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr {% if forloop.counter <= 3 %}class="top-3"{% endif %}>
                    <td class="rank-column">{{ forloop.counter }}</td>
                    <td>{{ user.username }}</td>
                    <td>{{ user.email }}</td>
                    <td class="points-column">{{ user.points }}</td>
                    <td>{{ user_stats|get_item:user.id|get_item:'invitation_count' }}</td>
                    <td>{{ user.created_at|date:"Y-m-d H:i:s" }}</td>
                    <td>
                        <a href="{% url 'admin:user_user_change' user.id %}">编辑</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7">没有找到符合条件的用户</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
