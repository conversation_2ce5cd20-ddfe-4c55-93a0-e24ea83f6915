# Generated by Django 4.2.10 on 2025-05-21 12:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=50, unique=True, verbose_name='设置键')),
                ('value', models.CharField(max_length=255, verbose_name='设置值')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '系统设置',
                'verbose_name_plural': '系统设置',
            },
        ),
        migrations.AlterModelOptions(
            name='invitationcode',
            options={'verbose_name': '邀请码', 'verbose_name_plural': '邀请码'},
        ),
        migrations.AddField(
            model_name='invitationcode',
            name='is_personal',
            field=models.BooleanField(default=False, verbose_name='是否个人专属'),
        ),
        migrations.AddField(
            model_name='user',
            name='inviter',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invited_users', to=settings.AUTH_USER_MODEL, verbose_name='邀请人'),
        ),
        migrations.AddField(
            model_name='user',
            name='points',
            field=models.IntegerField(default=0, verbose_name='积分'),
        ),
        migrations.AlterField(
            model_name='invitationcode',
            name='code',
            field=models.CharField(max_length=20, unique=True, verbose_name='邀请码'),
        ),
        migrations.AlterField(
            model_name='invitationcode',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='invitationcode',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_invitation_codes', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AlterField(
            model_name='invitationcode',
            name='is_used',
            field=models.BooleanField(default=False, verbose_name='是否已使用'),
        ),
        migrations.AlterField(
            model_name='invitationcode',
            name='used_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='使用时间'),
        ),
        migrations.AlterField(
            model_name='invitationcode',
            name='used_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='used_invitation_code', to=settings.AUTH_USER_MODEL, verbose_name='使用者'),
        ),
        migrations.CreateModel(
            name='InvitationRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('points_awarded', models.IntegerField(default=0, verbose_name='奖励积分')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('invitation_code', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitation_records', to='user.invitationcode', verbose_name='邀请码')),
                ('invitee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_invitation', to=settings.AUTH_USER_MODEL, verbose_name='被邀请人')),
                ('inviter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_invitations', to=settings.AUTH_USER_MODEL, verbose_name='邀请人')),
            ],
            options={
                'verbose_name': '邀请记录',
                'verbose_name_plural': '邀请记录',
            },
        ),
    ]
