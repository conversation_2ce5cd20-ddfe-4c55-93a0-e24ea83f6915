# Generated by Django 4.2.10 on 2025-05-21 14:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0003_initial_system_settings"),
    ]

    operations = [
        migrations.CreateModel(
            name="TemporaryInvitation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "invitation_code",
                    models.CharField(help_text="捕获的邀请码", max_length=8),
                ),
                (
                    "session_key",
                    models.<PERSON>r<PERSON>ield(
                        help_text="捕获邀请码时的Session Key",
                        max_length=40,
                        unique=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
