#!/usr/bin/env python3
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000"

def login_and_get_token():
    """登录并获取token"""
    login_url = f"{BASE_URL}/api/auth/login/"
    login_data = {
        "email": "<EMAIL>",
        "password": "liuzhao9575"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            print(f"获取到token: {token}")
            return token
        else:
            print(f"登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_technical_indicators_api(token):
    """测试技术指标API"""
    api_url = f"{BASE_URL}/api/crypto/technical-indicators/BTCUSDT/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    params = {
        "language": "en-US"
    }
    
    try:
        response = requests.get(api_url, headers=headers, params=params)
        print(f"\n技术指标API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("API响应数据结构:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查是否包含last_update_time字段
            if 'data' in data and 'last_update_time' in data['data']:
                print(f"\n✅ last_update_time字段存在: {data['data']['last_update_time']}")
            else:
                print("\n❌ last_update_time字段不存在")
                
        else:
            print(f"API请求失败: {response.text}")
    except Exception as e:
        print(f"API请求异常: {e}")

def main():
    print("开始测试API...")
    
    # 1. 登录获取token
    token = login_and_get_token()
    if not token:
        print("无法获取token，退出测试")
        return
    
    # 2. 测试技术指标API
    test_technical_indicators_api(token)

if __name__ == "__main__":
    main()
