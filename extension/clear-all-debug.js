// 彻底清除所有调试标志的脚本
// 在浏览器控制台中运行此脚本

console.log('🧹 开始清除所有调试标志...');

// 1. 清除 localStorage 中的调试标志
const debugKeys = [
  'i18n_debug',
  'debug',
  'app_debug',
  'vue_debug',
  'api_debug'
];

let clearedCount = 0;

debugKeys.forEach(key => {
  if (localStorage.getItem(key)) {
    localStorage.removeItem(key);
    console.log(`✅ 已清除 localStorage.${key}`);
    clearedCount++;
  }
});

// 2. 清除 sessionStorage 中的调试标志
debugKeys.forEach(key => {
  if (sessionStorage.getItem(key)) {
    sessionStorage.removeItem(key);
    console.log(`✅ 已清除 sessionStorage.${key}`);
    clearedCount++;
  }
});

// 3. 清除全局调试对象
const globalDebugObjects = [
  'i18nDebug',
  'appDebug',
  'vueDebug',
  'apiDebug'
];

globalDebugObjects.forEach(obj => {
  if (window[obj]) {
    delete window[obj];
    console.log(`✅ 已清除 window.${obj}`);
    clearedCount++;
  }
});

// 4. 清除可能的调试标志
if (window.localStorage) {
  // 清除所有包含 'debug' 的键
  Object.keys(localStorage).forEach(key => {
    if (key.toLowerCase().includes('debug')) {
      localStorage.removeItem(key);
      console.log(`✅ 已清除 localStorage.${key}`);
      clearedCount++;
    }
  });
}

console.log(`🎉 共清除了 ${clearedCount} 个调试标志！`);

if (clearedCount > 0) {
  console.log('🔄 正在刷新页面以应用更改...');
  // 5. 刷新页面
  setTimeout(() => {
    window.location.reload();
  }, 1000);
} else {
  console.log('✨ 没有发现需要清除的调试标志，应用已经是干净状态！');
}
