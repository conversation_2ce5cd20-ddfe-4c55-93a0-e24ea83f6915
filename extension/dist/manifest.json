{"manifest_version": 3, "name": "Cooltrade", "version": "1.0.0", "description": "Provides real-time technical indicator analysis and trading recommendations on cryptocurrency exchange pages", "permissions": ["activeTab", "storage", "tabs", "cookies"], "host_permissions": ["https://www.cooltrade.xyz/*"], "action": {"default_popup": "index.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "content_scripts": [{"matches": ["https://*.binance.com/*", "https://*.okx.com/*", "https://*.gate.io/*", "https://*.kucoin.com/*", "https://*.huobi.com/*", "https://*.bybit.com/*", "https://*.mexc.com/*", "https://*.bitget.com/*", "https://*.bitfinex.com/*", "https://*.kraken.com/*", "https://*.htx.com/*", "https://*.bitmart.com/*", "https://*.coinbase.com/*", "https://*.bitstamp.net/*", "https://*.poloniex.com/*", "https://*.bithumb.com/*", "https://*.upbit.com/*", "https://*.bitflyer.com/*", "https://*.gemini.com/*", "https://*.lbank.com/*", "https://*.phemex.com/*"], "js": ["assets/content.js"], "css": ["assets/main.css"]}], "background": {"service_worker": "assets/background.js"}, "web_accessible_resources": [{"resources": ["assets/*", "icons/*"], "matches": ["https://*.binance.com/*", "https://*.okx.com/*", "https://*.gate.io/*", "https://*.kucoin.com/*", "https://*.huobi.com/*", "https://*.bybit.com/*", "https://*.mexc.com/*", "https://*.bitget.com/*", "https://*.bitfinex.com/*", "https://*.kraken.com/*", "https://*.htx.com/*", "https://*.bitmart.com/*", "https://*.coinbase.com/*", "https://*.bitstamp.net/*", "https://*.poloniex.com/*", "https://*.bithumb.com/*", "https://*.upbit.com/*", "https://*.bitflyer.com/*", "https://*.gemini.com/*", "https://*.lbank.com/*", "https://*.phemex.com/*"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' https://www.cooltrade.xyz https://www.cooltrade.xyz/api https://api.coze.com; img-src 'self' https://readdy.ai data:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com;"}}